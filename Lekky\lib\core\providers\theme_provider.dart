import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/preference_keys.dart';
import '../shared/models/theme_mode.dart';
import '../models/theme_state.dart';
import '../di/service_locator.dart';
import '../theme/theme_manager.dart';

part 'theme_provider.g.dart';

/// Provider for theme management using Riverpod
@riverpod
class Theme extends _$Theme {
  @override
  Future<ThemeState> build() async {
    return await _loadTheme();
  }

  /// Load theme from SharedPreferences
  Future<ThemeState> _loadTheme() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeModeString = prefs.getString(PreferenceKeys.themeMode);
      
      AppThemeMode themeMode = AppThemeMode.system;
      if (themeModeString != null) {
        try {
          themeMode = AppThemeMode.values.firstWhere(
            (mode) => mode.toString() == themeModeString,
            orElse: () => AppThemeMode.system,
          );
        } catch (e) {
          themeMode = AppThemeMode.system;
        }
      }

      return ThemeState(
        themeMode: themeMode,
        isDarkMode: _isDarkMode(themeMode),
      );
    } catch (e) {
      // Return default theme on error
      return const ThemeState();
    }
  }

  /// Determine if dark mode should be active
  bool _isDarkMode(AppThemeMode themeMode) {
    switch (themeMode) {
      case AppThemeMode.light:
        return false;
      case AppThemeMode.dark:
        return true;
      case AppThemeMode.system:
        // For system mode, we'll need to check system brightness
        // For now, default to false (light mode)
        return false;
    }
  }

  /// Update theme mode
  Future<void> updateThemeMode(AppThemeMode mode) async {
    final currentState = await future;
    if (currentState.themeMode == mode) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(PreferenceKeys.themeMode, mode.toString());

      // Apply theme change immediately through ThemeManager
      try {
        final themeManager = serviceLocator<ThemeManager>();
        await themeManager.setThemeMode(mode);
      } catch (e) {
        // Handle error silently
      }

      // Update state
      state = AsyncValue.data(ThemeState(
        themeMode: mode,
        isDarkMode: _isDarkMode(mode),
      ));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Toggle between light and dark mode
  Future<void> toggleTheme() async {
    final currentState = await future;
    final newMode = currentState.themeMode == AppThemeMode.light
        ? AppThemeMode.dark
        : AppThemeMode.light;
    await updateThemeMode(newMode);
  }

  /// Set system theme mode
  Future<void> setSystemTheme() async {
    await updateThemeMode(AppThemeMode.system);
  }

  /// Set light theme mode
  Future<void> setLightTheme() async {
    await updateThemeMode(AppThemeMode.light);
  }

  /// Set dark theme mode
  Future<void> setDarkTheme() async {
    await updateThemeMode(AppThemeMode.dark);
  }
}
