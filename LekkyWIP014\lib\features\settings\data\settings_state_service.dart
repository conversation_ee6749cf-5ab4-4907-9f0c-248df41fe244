import 'package:shared_preferences/shared_preferences.dart';
import '../domain/models/settings_state.dart';

/// Service for preserving and restoring settings screen state
class SettingsStateService {
  static const String _stateKey = 'settings_preserved_state';

  /// Save settings state to persistent storage
  Future<bool> saveSettingsState(SettingsState state) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = state.toJsonString();
      return await prefs.setString(_stateKey, jsonString);
    } catch (e) {
      // Log error but don't throw - state preservation is not critical
      return false;
    }
  }

  /// Restore settings state from persistent storage
  /// Returns null if no valid state exists
  Future<SettingsState?> restoreSettingsState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_stateKey);
      
      if (jsonString == null) {
        return null;
      }

      final state = SettingsState.fromJsonString(jsonString);
      
      // Check if state is still valid (within 30 minutes)
      if (!state.isValid) {
        // Clear expired state
        await clearSettingsState();
        return null;
      }

      return state;
    } catch (e) {
      // If there's any error parsing, clear the state and return null
      await clearSettingsState();
      return null;
    }
  }

  /// Clear preserved settings state
  Future<bool> clearSettingsState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove(_stateKey);
    } catch (e) {
      return false;
    }
  }

  /// Check if preserved state exists and is valid
  Future<bool> hasValidState() async {
    final state = await restoreSettingsState();
    return state != null;
  }
}
