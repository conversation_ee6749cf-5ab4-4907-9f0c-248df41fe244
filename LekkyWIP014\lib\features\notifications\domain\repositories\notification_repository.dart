import '../models/notification.dart';

/// Repository interface for notifications
abstract class NotificationRepository {
  /// Get all notifications
  Future<List<AppNotification>> getAllNotifications();
  
  /// Get unread notifications
  Future<List<AppNotification>> getUnreadNotifications();
  
  /// Get notification by id
  Future<AppNotification?> getNotificationById(int id);
  
  /// Add a notification
  Future<int> addNotification(AppNotification notification);
  
  /// Mark a notification as read
  Future<void> markAsRead(int id);
  
  /// Mark all notifications as read
  Future<void> markAllAsRead();
  
  /// Delete a notification
  Future<void> deleteNotification(int id);
  
  /// Delete all notifications
  Future<void> deleteAllNotifications();
  
  /// Get the count of unread notifications
  Future<int> getUnreadCount();
}
