// File: lib/features/validation/domain/models/integrity_report.dart
import 'validation_issue.dart';

/// Model class for data integrity reports
class IntegrityReport {
  /// When the report was generated
  final DateTime generatedAt;
  
  /// List of validation issues found
  final List<ValidationIssue> issues;
  
  /// Total number of entries checked
  final int totalEntriesChecked;
  
  /// Number of valid entries
  final int validEntriesCount;
  
  /// Number of invalid entries
  final int invalidEntriesCount;
  
  /// How long the check took to run
  final Duration checkDuration;

  /// Constructor
  IntegrityReport({
    required this.issues,
    required this.totalEntriesChecked,
    required this.validEntriesCount,
    required this.invalidEntriesCount,
    required this.checkDuration,
    DateTime? generatedAt,
  }) : generatedAt = generatedAt ?? DateTime.now();
  
  /// Create a copy of this report with optional new values
  IntegrityReport copyWith({
    DateTime? generatedAt,
    List<ValidationIssue>? issues,
    int? totalEntriesChecked,
    int? validEntriesCount,
    int? invalidEntriesCount,
    Duration? checkDuration,
  }) {
    return IntegrityReport(
      generatedAt: generatedAt ?? this.generatedAt,
      issues: issues ?? this.issues,
      totalEntriesChecked: totalEntriesChecked ?? this.totalEntriesChecked,
      validEntriesCount: validEntriesCount ?? this.validEntriesCount,
      invalidEntriesCount: invalidEntriesCount ?? this.invalidEntriesCount,
      checkDuration: checkDuration ?? this.checkDuration,
    );
  }
  
  /// Convert to a map for database operations
  Map<String, dynamic> toMap() {
    return {
      'generated_at': generatedAt.toIso8601String(),
      'issues': issues.map((issue) => issue.toMap()).toList(),
      'total_entries_checked': totalEntriesChecked,
      'valid_entries_count': validEntriesCount,
      'invalid_entries_count': invalidEntriesCount,
      'check_duration_ms': checkDuration.inMilliseconds,
    };
  }
  
  /// Create a report from a map
  factory IntegrityReport.fromMap(Map<String, dynamic> map) {
    return IntegrityReport(
      generatedAt: DateTime.parse(map['generated_at'] as String),
      issues: (map['issues'] as List<dynamic>)
          .map((issueMap) => ValidationIssue.fromMap(issueMap as Map<String, dynamic>))
          .toList(),
      totalEntriesChecked: map['total_entries_checked'] as int,
      validEntriesCount: map['valid_entries_count'] as int,
      invalidEntriesCount: map['invalid_entries_count'] as int,
      checkDuration: Duration(milliseconds: map['check_duration_ms'] as int),
    );
  }
  
  /// Get the percentage of valid entries
  double get validPercentage => 
      totalEntriesChecked > 0 ? (validEntriesCount / totalEntriesChecked * 100) : 0;
  
  /// Get the percentage of invalid entries
  double get invalidPercentage => 
      totalEntriesChecked > 0 ? (invalidEntriesCount / totalEntriesChecked * 100) : 0;
  
  /// Get the count of high severity issues
  int get highSeverityCount => 
      issues.where((issue) => issue.severity == ValidationIssueSeverity.high).length;
  
  /// Get the count of medium severity issues
  int get mediumSeverityCount => 
      issues.where((issue) => issue.severity == ValidationIssueSeverity.medium).length;
  
  /// Get the count of low severity issues
  int get lowSeverityCount => 
      issues.where((issue) => issue.severity == ValidationIssueSeverity.low).length;
  
  @override
  String toString() {
    return 'IntegrityReport(generatedAt: $generatedAt, issues: ${issues.length}, '
        'totalEntriesChecked: $totalEntriesChecked, '
        'validEntriesCount: $validEntriesCount, '
        'invalidEntriesCount: $invalidEntriesCount)';
  }
}
