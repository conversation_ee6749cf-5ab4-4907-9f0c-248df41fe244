import 'package:freezed_annotation/freezed_annotation.dart';
import '../shared/models/theme_mode.dart';

part 'theme_state.freezed.dart';
part 'theme_state.g.dart';

/// Immutable state model for theme configuration
@freezed
class ThemeState with _$ThemeState {
  const factory ThemeState({
    /// Current theme mode
    @Default(AppThemeMode.system) AppThemeMode themeMode,
    
    /// Whether dark mode is currently active
    @Default(false) bool isDarkMode,
  }) = _ThemeState;

  factory ThemeState.fromJson(Map<String, dynamic> json) =>
      _$ThemeStateFromJson(json);
}

/// Extension methods for ThemeState
extension ThemeStateExtension on ThemeState {
  /// Get display name for current theme mode
  String get themeModeDisplayName {
    switch (themeMode) {
      case AppThemeMode.light:
        return 'Light';
      case AppThemeMode.dark:
        return 'Dark';
      case AppThemeMode.system:
        return 'System';
    }
  }
  
  /// Get icon for current theme mode
  String get themeModeIcon {
    switch (themeMode) {
      case AppThemeMode.light:
        return '☀️';
      case AppThemeMode.dark:
        return '🌙';
      case AppThemeMode.system:
        return '⚙️';
    }
  }
  
  /// Check if theme is system-controlled
  bool get isSystemControlled => themeMode == AppThemeMode.system;
  
  /// Check if theme is manually set to light
  bool get isLightMode => themeMode == AppThemeMode.light;
  
  /// Check if theme is manually set to dark
  bool get isDarkModeManual => themeMode == AppThemeMode.dark;
}
