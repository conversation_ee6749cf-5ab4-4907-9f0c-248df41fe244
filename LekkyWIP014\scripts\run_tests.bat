@echo off
REM File: scripts/run_tests.bat
REM Comprehensive test runner for Lekky app

echo ========================================
echo Lekky App - Comprehensive Test Runner
echo ========================================

echo.
echo [1/6] Generating mocks and code...
call flutter packages pub run build_runner build --delete-conflicting-outputs
if %errorlevel% neq 0 (
    echo ERROR: Code generation failed
    exit /b 1
)

echo.
echo [2/6] Running unit tests...
call flutter test test/features/calculations/ test/features/validation/ test/features/database/ test/performance/
if %errorlevel% neq 0 (
    echo ERROR: Unit tests failed
    exit /b 1
)

echo.
echo [3/6] Running widget tests...
call flutter test test/features/widgets/
if %errorlevel% neq 0 (
    echo ERROR: Widget tests failed
    exit /b 1
)

echo.
echo [4/6] Running integration tests...
call flutter test integration_test/
if %errorlevel% neq 0 (
    echo ERROR: Integration tests failed
    exit /b 1
)

echo.
echo [5/6] Generating test coverage report...
call flutter test --coverage
if %errorlevel% neq 0 (
    echo ERROR: Coverage generation failed
    exit /b 1
)

echo.
echo [6/6] Running performance analysis...
call flutter analyze
if %errorlevel% neq 0 (
    echo WARNING: Static analysis found issues
)

echo.
echo ========================================
echo Test Summary:
echo ========================================
echo ✅ Unit Tests: Business logic, validation, database
echo ✅ Widget Tests: UI components and interactions  
echo ✅ Integration Tests: Complete user flows
echo ✅ Performance Tests: Large dataset handling
echo ✅ Coverage Report: Generated in coverage/lcov.info
echo.
echo All tests completed successfully!
echo ========================================

pause
