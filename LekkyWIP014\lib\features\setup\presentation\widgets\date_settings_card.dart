import 'package:flutter/material.dart';
import '../../domain/models/date_format.dart';
import 'setup_section_header.dart';
import 'radio_option.dart';

/// A widget for date settings in the setup screen
class DateSettingsCard extends StatelessWidget {
  /// Current date format
  final DateFormat dateFormat;
  
  /// Whether to show time with date
  final bool showTimeWithDate;
  
  /// Callback when date format changes
  final Function(DateFormat) onDateFormatChanged;
  
  /// Callback when show time with date changes
  final Function(bool) onShowTimeWithDateChanged;
  
  /// Constructor
  const DateSettingsCard({
    Key? key,
    required this.dateFormat,
    required this.showTimeWithDate,
    required this.onDateFormatChanged,
    required this.onShowTimeWithDateChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SetupSectionHeader(
              title: 'Date Settings',
              description: 'Choose how dates will be displayed throughout the app.',
              icon: Icons.calendar_today,
            ),
            
            // Date Format Subsection
            const Text(
              'Date Format',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            
            RadioOption<DateFormat>(
              value: DateFormat.ddMmYyyy,
              groupValue: dateFormat,
              onChanged: onDateFormatChanged,
              title: 'DD-MM-YYYY',
              description: 'Example: 11-05-2025',
            ),
            
            RadioOption<DateFormat>(
              value: DateFormat.mmDdYyyy,
              groupValue: dateFormat,
              onChanged: onDateFormatChanged,
              title: 'MM-DD-YYYY',
              description: 'Example: 05-11-2025',
            ),
            
            RadioOption<DateFormat>(
              value: DateFormat.yyyyMmDd,
              groupValue: dateFormat,
              onChanged: onDateFormatChanged,
              title: 'YYYY-MM-DD',
              description: 'Example: 2025-05-11',
            ),
            
            const SizedBox(height: 24),
            
            // Date Information Subsection
            const Text(
              'Date Information',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            const Text(
              'Choose whether to show just the date or both date and time for your meter readings.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),
            
            RadioOption<bool>(
              value: false,
              groupValue: showTimeWithDate,
              onChanged: onShowTimeWithDateChanged,
              title: 'Date only',
              description: 'Example: 11-05-2025',
            ),
            
            RadioOption<bool>(
              value: true,
              groupValue: showTimeWithDate,
              onChanged: onShowTimeWithDateChanged,
              title: 'Date and time',
              description: 'Example: 11-05-2025 17:28',
            ),
          ],
        ),
      ),
    );
  }
}
