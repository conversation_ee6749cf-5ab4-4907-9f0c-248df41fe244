import 'package:flutter/material.dart';

/// A widget for currency options in the setup screen
class CurrencyOption extends StatelessWidget {
  /// Currency code
  final String code;

  /// Currency symbol
  final String symbol;

  /// Currency name
  final String name;

  /// Whether the currency is selected
  final bool isSelected;

  /// Callback when currency is selected
  final Function(String, String) onSelected;

  /// Constructor
  const CurrencyOption({
    Key? key,
    required this.code,
    required this.symbol,
    required this.name,
    required this.isSelected,
    required this.onSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Use MediaQuery to make the widget responsive
    final screenWidth = MediaQuery.of(context).size.width;
    final symbolFontSize = screenWidth < 360 ? 14.0 : 16.0;
    final codeFontSize = screenWidth < 360 ? 9.0 : 10.0;

    return InkWell(
      onTap: () => onSelected(code, symbol),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).dividerColor,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
          color: isSelected
              ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
              : null,
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  symbol,
                  style: TextStyle(
                    fontSize: symbolFontSize,
                    fontWeight: FontWeight.bold,
                    color: isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).textTheme.bodyLarge?.color,
                  ),
                ),
              ),
              const SizedBox(height: 1),
              FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  code,
                  style: TextStyle(
                    fontSize: codeFontSize,
                    color: isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).textTheme.bodySmall?.color,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
