// File: test/core/utils/bulk_operation_context_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/utils/bulk_operation_context.dart';

void main() {
  group('BulkOperationContext', () {
    setUp(() {
      // Reset context before each test
      BulkOperationContext.reset();
    });

    test('should start and end bulk operation', () {
      // Arrange & Act
      expect(BulkOperationContext.isBulkOperation, isFalse);
      
      BulkOperationContext.startBulkOperation();
      expect(BulkOperationContext.isBulkOperation, isTrue);
      
      BulkOperationContext.endBulkOperation();
      expect(BulkOperationContext.isBulkOperation, isFalse);
    });

    test('should track operation count', () {
      // Arrange
      BulkOperationContext.startBulkOperation();
      
      // Act
      expect(BulkOperationContext.operationCount, equals(0));
      
      BulkOperationContext.incrementOperationCount();
      expect(BulkOperationContext.operationCount, equals(1));
      
      BulkOperationContext.incrementOperationCount();
      expect(BulkOperationContext.operationCount, equals(2));
      
      // Cleanup
      BulkOperationContext.endBulkOperation();
    });

    test('should not increment count when not in bulk operation', () {
      // Arrange & Act
      expect(BulkOperationContext.isBulkOperation, isFalse);
      
      BulkOperationContext.incrementOperationCount();
      expect(BulkOperationContext.operationCount, equals(0));
    });

    test('should handle multiple start calls gracefully', () {
      // Arrange & Act
      BulkOperationContext.startBulkOperation();
      expect(BulkOperationContext.isBulkOperation, isTrue);
      
      // Should not change state
      BulkOperationContext.startBulkOperation();
      expect(BulkOperationContext.isBulkOperation, isTrue);
      
      // Cleanup
      BulkOperationContext.endBulkOperation();
    });

    test('should handle end call without start gracefully', () {
      // Arrange & Act
      expect(BulkOperationContext.isBulkOperation, isFalse);
      
      // Should not throw or change state
      BulkOperationContext.endBulkOperation();
      expect(BulkOperationContext.isBulkOperation, isFalse);
    });

    test('should reset context properly', () {
      // Arrange
      BulkOperationContext.startBulkOperation();
      BulkOperationContext.incrementOperationCount();
      BulkOperationContext.incrementOperationCount();
      
      expect(BulkOperationContext.isBulkOperation, isTrue);
      expect(BulkOperationContext.operationCount, equals(2));
      
      // Act
      BulkOperationContext.reset();
      
      // Assert
      expect(BulkOperationContext.isBulkOperation, isFalse);
      expect(BulkOperationContext.operationCount, equals(0));
    });

    test('should wait for completion', () async {
      // Arrange
      BulkOperationContext.startBulkOperation();
      
      // Act - start waiting in background
      final waitFuture = BulkOperationContext.waitForCompletion();
      
      // Complete the operation
      BulkOperationContext.endBulkOperation();
      
      // Should complete without timeout
      await expectLater(
        waitFuture.timeout(Duration(milliseconds: 100)),
        completes,
      );
    });
  });
}
