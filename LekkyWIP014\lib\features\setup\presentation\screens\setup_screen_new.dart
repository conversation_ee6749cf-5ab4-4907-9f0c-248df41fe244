import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/setup_controller.dart';
import '../widgets/date_settings_card.dart';
import '../widgets/alert_settings_card.dart';
import '../widgets/meter_reading_card.dart';
import '../widgets/region_settings_card.dart';
import '../widgets/appearance_settings_card.dart';
import '../../../../core/shared/models/theme_mode.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_banner.dart';

/// Setup screen for configuring app preferences
class SetupScreen extends StatefulWidget {
  /// Constructor
  const SetupScreen({super.key});

  @override
  State<SetupScreen> createState() => _SetupScreenState();
}

class _SetupScreenState extends State<SetupScreen> {
  late SetupController _controller;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _controller = SetupController();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return ChangeNotifierProvider.value(
      value: _controller,
      child: Scaffold(
        backgroundColor:
            AppColors.getAppBarColor('settings', isDark).withOpacity(0.05),
        body: Column(
          children: [
            // App Banner with "Setup" title using gradient colors
            AppBanner(
              message: 'Setup',
              gradientColors: AppColors.getSettingsMainCardGradient(isDark),
              textColor: AppColors.getAppBarTextColor('settings', isDark),
            ),
            Expanded(
              child: Consumer<SetupController>(
                builder: (context, controller, _) {
                  if (controller.isLoading) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  if (controller.error != null) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.error_outline,
                            color: Colors.red,
                            size: 48,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Error: ${controller.error}',
                            style: const TextStyle(color: Colors.red),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () {
                              setState(() {
                                _controller = SetupController();
                              });
                            },
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    );
                  }

                  return Column(
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          controller: _scrollController,
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Region Settings
                              const Text(
                                'Region Settings',
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                'Configure language and currency preferences.',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey,
                                ),
                              ),
                              const SizedBox(height: 16),
                              RegionSettingsCard(
                                language: controller.preferences.language,
                                currency: controller.preferences.currency,
                                currencySymbol:
                                    controller.preferences.currencySymbol,
                                onLanguageChanged: controller.setLanguage,
                                onCurrencyChanged: controller.setCurrency,
                              ),

                              const SizedBox(height: 32),

                              // First Meter Reading
                              const Text(
                                'Initial Meter Reading',
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                'Enter your current meter reading to start tracking.',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey,
                                ),
                              ),
                              const SizedBox(height: 16),
                              MeterReadingCard(
                                initialMeterReading:
                                    controller.preferences.initialMeterReading,
                                onInitialMeterReadingChanged:
                                    controller.setInitialMeterReading,
                                currencySymbol:
                                    controller.preferences.currencySymbol,
                              ),

                              const SizedBox(height: 32),

                              // Alerts & Notifications
                              const Text(
                                'Alert Settings',
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                'Configure when you want to receive alerts about your meter balance.',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey,
                                ),
                              ),
                              const SizedBox(height: 16),
                              AlertSettingsCard(
                                alertThreshold:
                                    controller.preferences.alertThreshold,
                                daysInAdvance:
                                    controller.preferences.daysInAdvance,
                                currencySymbol:
                                    controller.preferences.currencySymbol,
                                onAlertThresholdChanged:
                                    controller.setAlertThreshold,
                                onDaysInAdvanceChanged:
                                    controller.setDaysInAdvance,
                              ),

                              const SizedBox(height: 32),

                              // Date Settings
                              const Text(
                                'Date Settings',
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                'Configure how dates are displayed in the app.',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey,
                                ),
                              ),
                              const SizedBox(height: 16),
                              DateSettingsCard(
                                dateFormat: controller.preferences.dateFormat,
                                showTimeWithDate:
                                    controller.preferences.showTimeWithDate,
                                onDateFormatChanged: controller.setDateFormat,
                                onShowTimeWithDateChanged:
                                    controller.setShowTimeWithDate,
                              ),

                              const SizedBox(height: 32),

                              // Appearance
                              const Text(
                                'Appearance',
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                'Customize the look and feel of the app.',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey,
                                ),
                              ),
                              const SizedBox(height: 16),
                              AppearanceSettingsCard(
                                themeMode: controller.preferences.themeMode,
                                onThemeModeChanged: controller.setThemeMode,
                              ),

                              const SizedBox(height: 32),
                            ],
                          ),
                        ),
                      ),
                      _buildNavigationButtons(controller),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationButtons(SetupController controller) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ElevatedButton(
            onPressed: () async {
              if (controller.validateCurrentStep()) {
                final success = await controller.savePreferences();
                if (success) {
                  await controller.completeSetup();
                  if (mounted) {
                    Navigator.of(context).pushReplacementNamed('/home');
                  }
                } else {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                            'Error: ${controller.error ?? "Unknown error"}'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please check your inputs.'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('Finish Setup'),
          ),
        ],
      ),
    );
  }
}
