import 'package:flutter/material.dart';
import '../../../../core/utils/date_time_utils.dart';

/// A card that displays top-up statistics
class TopUpStatisticsCard extends StatelessWidget {
  /// Days to alert threshold
  final double? daysToAlertThreshold;

  /// Days to meter zero
  final double? daysToMeterZero;

  /// Currency symbol to use
  final String currencySymbol;

  /// Alert threshold value
  final double alertThreshold;

  /// Days in advance setting
  final int daysInAdvance;

  /// Date format preference
  final String dateFormat;

  /// Constructor
  const TopUpStatisticsCard({
    super.key,
    required this.daysToAlertThreshold,
    required this.daysToMeterZero,
    this.currencySymbol = '₦',
    required this.alertThreshold,
    required this.daysInAdvance,
    required this.dateFormat,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with icon and title
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  color: theme.colorScheme.onSurface,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Top Up Statistics',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatisticItem(
                    context,
                    'Threshold',
                    daysToAlertThreshold,
                    Icons.notification_important,
                    theme.colorScheme.primary,
                    isThreshold: true,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatisticItem(
                    context,
                    'Meter Zero',
                    daysToMeterZero,
                    Icons.battery_0_bar,
                    theme.colorScheme.secondary,
                    isThreshold: false,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Based on your usage patterns',
              style: TextStyle(
                fontSize: 12,
                color: theme.colorScheme.onSurface.withOpacity(0.6),
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Build a single statistic item
  Widget _buildStatisticItem(
    BuildContext context,
    String title,
    double? value,
    IconData icon,
    Color color, {
    required bool isThreshold,
  }) {
    final theme = Theme.of(context);
    final isExceeded = value == -1;

    // Get days and date text separately
    final daysText = isThreshold
        ? DateTimeUtils.getThresholdDaysText(value, isExceeded: isExceeded)
        : DateTimeUtils.getMeterZeroDaysText(value);

    final dateText = isThreshold
        ? DateTimeUtils.getThresholdDateText(value, dateFormat,
            isExceeded: isExceeded)
        : DateTimeUtils.getMeterZeroDateText(value, dateFormat);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Row 1: Title with icon
          Row(
            children: [
              Icon(
                icon,
                color: color,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          // Row 2: Days text
          Text(
            daysText,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
          // Row 3: Date text (if available)
          if (dateText.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              dateText,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
