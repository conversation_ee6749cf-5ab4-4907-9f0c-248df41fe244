import '../../../../core/utils/logger.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../../top_ups/domain/models/top_up.dart';
import '../../../top_ups/domain/repositories/top_up_repository.dart';

/// Centralized service for meter reading validation logic
class MeterReadingValidator {
  final MeterReadingRepository _meterReadingRepository;
  final TopUpRepository _topUpRepository;
  final logger = Logger('MeterReadingValidator');

  /// Constructor
  MeterReadingValidator({
    required MeterReadingRepository meterReadingRepository,
    required TopUpRepository topUpRepository,
  })  : _meterReadingRepository = meterReadingRepository,
        _topUpRepository = topUpRepository;

  /// Validate a single meter reading for balance consistency
  ///
  /// Uses balance accumulation model: current reading should be ≤ previous reading + top-ups
  /// (allowing for reasonable consumption between readings)
  Future<bool> validateMeterReading(MeterReading meterReading) async {
    try {
      // Get all meter readings and top-ups for comprehensive validation
      final allReadings = await _meterReadingRepository.getAllMeterReadings();
      final allTopUps = await _topUpRepository.getAllTopUps();

      // Sort readings by date
      final sortedReadings = List<MeterReading>.from(allReadings)
        ..sort((a, b) => a.date.compareTo(b.date));

      // Find the index of the current reading
      int currentIndex = -1;
      for (int i = 0; i < sortedReadings.length; i++) {
        if (sortedReadings[i].id == meterReading.id ||
            (sortedReadings[i].date == meterReading.date &&
                sortedReadings[i].value == meterReading.value)) {
          currentIndex = i;
          break;
        }
      }

      // If this is the first reading or not found in existing readings, it's valid
      if (currentIndex <= 0) {
        return true;
      }

      final previousReading = sortedReadings[currentIndex - 1];

      // Find top-ups between the previous reading and this one
      final topUpsBetween = allTopUps
          .where((topUp) =>
              topUp.date.isAfter(previousReading.date) &&
              topUp.date.isBefore(meterReading.date))
          .toList();

      // Calculate total top-up amount
      final totalTopUp =
          topUpsBetween.fold<double>(0, (sum, topUp) => sum + topUp.amount);

      // Calculate maximum expected reading (balance accumulation model)
      final maxExpectedReading = previousReading.value + totalTopUp;

      // Check if current reading is within valid range
      // Allow for reasonable consumption (current ≤ previous + top-ups)
      // Also check for unrealistic consumption over 1 week period
      final daysBetween =
          meterReading.date.difference(previousReading.date).inDays;
      if (daysBetween > 7) {
        // For periods > 1 week, allow more lenient validation
        return meterReading.value >= 0 &&
            meterReading.value <= maxExpectedReading;
      }

      // For normal periods, current reading should be ≤ previous + top-ups
      return meterReading.value >= 0 &&
          meterReading.value <= maxExpectedReading + 0.01;
    } catch (e) {
      logger.e('Failed to validate meter reading', details: e.toString());
      return true; // Default to valid in case of error
    }
  }

  /// Validate multiple meter readings for balance consistency
  Future<Map<int, bool>> validateMeterReadings(
      List<MeterReading> readings) async {
    final results = <int, bool>{};

    try {
      // Get all top-ups once for efficiency
      final allTopUps = await _topUpRepository.getAllTopUps();

      // Sort readings by date
      final sortedReadings = List<MeterReading>.from(readings)
        ..sort((a, b) => a.date.compareTo(b.date));

      // Validate each reading
      for (int i = 0; i < sortedReadings.length; i++) {
        final currentReading = sortedReadings[i];

        // First reading is always valid
        if (i == 0) {
          results[currentReading.id!] = true;
          continue;
        }

        final previousReading = sortedReadings[i - 1];

        // Find top-ups between the previous reading and this one
        final topUpsBetween = allTopUps
            .where((topUp) =>
                topUp.date.isAfter(previousReading.date) &&
                topUp.date.isBefore(currentReading.date))
            .toList();

        // Calculate total top-up amount
        final totalTopUp =
            topUpsBetween.fold<double>(0, (sum, topUp) => sum + topUp.amount);

        // Calculate maximum expected reading (balance accumulation model)
        final maxExpectedReading = previousReading.value + totalTopUp;

        // Check if current reading is within valid range
        final daysBetween =
            currentReading.date.difference(previousReading.date).inDays;
        bool isValid;
        if (daysBetween > 7) {
          // For periods > 1 week, allow more lenient validation
          isValid = currentReading.value >= 0 &&
              currentReading.value <= maxExpectedReading;
        } else {
          // For normal periods, current reading should be ≤ previous + top-ups
          isValid = currentReading.value >= 0 &&
              currentReading.value <= maxExpectedReading + 0.01;
        }
        results[currentReading.id!] = isValid;
      }
    } catch (e) {
      logger.e('Failed to validate meter readings', details: e.toString());
      // Default all to valid in case of error
      for (final reading in readings) {
        results[reading.id!] = true;
      }
    }

    return results;
  }

  /// Calculate expected meter reading value based on previous reading and top-ups
  Future<double?> calculateExpectedReading(MeterReading meterReading) async {
    try {
      // Get all meter readings and top-ups
      final allReadings = await _meterReadingRepository.getAllMeterReadings();
      final allTopUps = await _topUpRepository.getAllTopUps();

      // Sort readings by date
      final sortedReadings = List<MeterReading>.from(allReadings)
        ..sort((a, b) => a.date.compareTo(b.date));

      // Find the previous reading
      MeterReading? previousReading;
      for (final reading in sortedReadings) {
        if (reading.date.isBefore(meterReading.date)) {
          previousReading = reading;
        } else {
          break;
        }
      }

      // If no previous reading, return null
      if (previousReading == null) {
        return null;
      }

      // Find top-ups between the previous reading and this one
      final topUpsBetween = allTopUps
          .where((topUp) =>
              topUp.date.isAfter(previousReading!.date) &&
              topUp.date.isBefore(meterReading.date))
          .toList();

      // Calculate total top-up amount
      final totalTopUp =
          topUpsBetween.fold<double>(0, (sum, topUp) => sum + topUp.amount);

      // Calculate maximum expected reading (balance accumulation model)
      return previousReading.value + totalTopUp;
    } catch (e) {
      logger.e('Failed to calculate expected reading', details: e.toString());
      return null;
    }
  }

  /// Validate a single top-up for basic validation rules
  Future<bool> validateTopUp(TopUp topUp) async {
    try {
      // Check for negative values
      if (topUp.amount < 0) {
        return false;
      }

      // Check for maximum value (999.99)
      if (topUp.amount > 999.99) {
        return false;
      }

      // Check for future dates
      if (topUp.date.isAfter(DateTime.now())) {
        return false;
      }

      return true;
    } catch (e) {
      logger.e('Failed to validate top-up', details: e.toString());
      return true; // Default to valid in case of error
    }
  }

  /// Validate multiple top-ups for basic validation rules
  Future<Map<int, bool>> validateTopUps(List<TopUp> topUps) async {
    final results = <int, bool>{};

    try {
      for (final topUp in topUps) {
        final isValid = await validateTopUp(topUp);
        results[topUp.id!] = isValid;
      }
    } catch (e) {
      logger.e('Failed to validate top-ups', details: e.toString());
      // Default all to valid in case of error
      for (final topUp in topUps) {
        results[topUp.id!] = true;
      }
    }

    return results;
  }
}
