import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/shared/widgets/currency_input_field.dart';
import 'setup_section_header.dart';
import 'info_notice.dart';

/// A widget for meter reading settings in the setup screen
class MeterReadingCard extends StatelessWidget {
  /// Current initial meter reading
  final double? initialMeterReading;

  /// Callback when initial meter reading changes
  final Function(double?) onInitialMeterReadingChanged;

  /// Currency symbol to use
  final String currencySymbol;

  /// Constructor
  const MeterReadingCard({
    Key? key,
    required this.initialMeterReading,
    required this.onInitialMeterReadingChanged,
    this.currencySymbol = '£',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SetupSectionHeader(
              title: 'Initial Meter Reading',
              description:
                  'Enter your current meter reading to start tracking.',
              icon: Icons.speed,
            ),

            const InfoNotice(
              message:
                  'This is optional. You can skip this step and add your first meter reading later.',
              icon: Icons.info_outline,
            ),

            const SizedBox(height: 16),

            CurrencyInputField(
              value: initialMeterReading,
              onChanged: onInitialMeterReadingChanged,
              currencySymbol: currencySymbol,
              labelText: 'Current Meter Reading',
              hintText: 'Enter your current meter reading',
              helperText: 'Enter the number shown on your electricity meter',
              minValue: 0.0,
              maxValue: 9999.99,
              allowNull: true,
              borderRadius: BorderRadius.circular(8),
            ),

            const SizedBox(height: 16),

            const Text(
              'How to read your meter:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),

            // Meter reading instructions
            _buildInstructionStep(
              context,
              '1',
              'Locate your prepaid electricity meter.',
            ),
            _buildInstructionStep(
              context,
              '2',
              'Press the display button until you see the current reading.',
            ),
            _buildInstructionStep(
              context,
              '3',
              'Enter the number shown in your currency units.',
            ),
            _buildInstructionStep(
              context,
              '4',
              'Some meters show multiple values - look for the one labeled "Total" or similar.',
            ),

            const SizedBox(height: 16),

            Text(
              'Tip: Take a photo of your meter for future reference.',
              style: TextStyle(
                fontSize: 12,
                fontStyle: FontStyle.italic,
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionStep(
      BuildContext context, String number, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                number,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(text),
          ),
        ],
      ),
    );
  }
}
