#Sun Jun 01 19:34:18 BST 2025
path.4=8/classes.dex
path.3=1/classes.dex
path.2=0/classes.dex
path.1=0/classes.dex
path.0=classes.dex
base.4=D\:\\000.Workspace\\Lekky\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\8\\classes.dex
base.3=D\:\\000.Workspace\\Lekky\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\1\\classes.dex
base.2=D\:\\000.Workspace\\Lekky\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.1=D\:\\000.Workspace\\Lekky\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\0\\classes.dex
base.0=D\:\\000.Workspace\\Lekky\\build\\app\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
renamed.3=classes4.dex
renamed.2=classes3.dex
renamed.1=classes2.dex
renamed.0=classes.dex
renamed.4=classes5.dex
