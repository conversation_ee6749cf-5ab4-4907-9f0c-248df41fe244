import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/preference_keys.dart';
import '../shared/models/date_format.dart';
import '../shared/models/theme_mode.dart';
import '../models/settings_state.dart';
import '../di/service_locator.dart';
import '../services/preference_service.dart';
import '../providers/localization_provider.dart';

part 'settings_provider.g.dart';

/// Provider for comprehensive settings management using Riverpod
@riverpod
class Settings extends _$Settings {
  @override
  Future<SettingsState> build() async {
    return await _loadSettings();
  }

  /// Load settings from SharedPreferences
  Future<SettingsState> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      return SettingsState(
        language: prefs.getString(PreferenceKeys.language) ?? 'English',
        currency: prefs.getString(PreferenceKeys.currency) ?? 'GBP',
        currencySymbol: prefs.getString(PreferenceKeys.currencySymbol) ?? '£',
        alertThreshold: prefs.getDouble(PreferenceKeys.alertThreshold) ?? 5.0,
        daysInAdvance: prefs.getInt(PreferenceKeys.daysInAdvance) ?? 5,
        notificationsEnabled:
            prefs.getBool(PreferenceKeys.notificationsEnabled) ?? true,
        remindersEnabled:
            prefs.getBool(PreferenceKeys.remindersEnabled) ?? true,
        lowBalanceAlertsEnabled:
            prefs.getBool('low_balance_alerts_enabled') ?? true,
        timeToTopUpAlertsEnabled:
            prefs.getBool('time_to_top_up_alerts_enabled') ?? true,
        invalidRecordAlertsEnabled:
            prefs.getBool('invalid_record_alerts_enabled') ?? true,
        reminderFrequency: prefs.getString('reminder_frequency') ?? 'weekly',
        reminderStartDateTime: _parseDateTime(
            prefs.getString(PreferenceKeys.reminderStartDateTime)),
        dateFormat: prefs.getString(PreferenceKeys.dateFormat) ?? 'DD-MM-YYYY',
        showTimeWithDate:
            prefs.getBool(PreferenceKeys.showTimeWithDate) ?? false,
        themeMode: _parseThemeMode(prefs.getString(PreferenceKeys.themeMode)),
      );
    } catch (e) {
      // Return default settings on error
      return const SettingsState();
    }
  }

  /// Parse DateTime from string
  DateTime? _parseDateTime(String? dateTimeString) {
    if (dateTimeString == null) return null;
    try {
      return DateTime.parse(dateTimeString);
    } catch (e) {
      return null;
    }
  }

  /// Parse theme mode from string
  AppThemeMode _parseThemeMode(String? themeModeString) {
    if (themeModeString == null) return AppThemeMode.system;
    try {
      return AppThemeMode.values.firstWhere(
        (mode) => mode.toString() == themeModeString,
        orElse: () => AppThemeMode.system,
      );
    } catch (e) {
      return AppThemeMode.system;
    }
  }

  /// Update language
  Future<void> updateLanguage(String language) async {
    final currentState = await future;
    if (currentState.language == language) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(PreferenceKeys.language, language);

      // Update localization provider
      try {
        final localizationProvider = serviceLocator<LocalizationProvider>();
        await localizationProvider.setLocale(_getLanguageCode(language));
      } catch (e) {
        // Handle error silently
      }

      // Update PreferenceService for synchronization
      try {
        final preferenceService = serviceLocator<PreferenceService>();
        await preferenceService.setLanguage(language);
      } catch (e) {
        // Handle error silently
      }

      // Update state
      state = AsyncValue.data(currentState.copyWith(language: language));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Convert language name to language code
  String _getLanguageCode(String language) {
    switch (language) {
      case 'English':
        return 'en';
      case 'Spanish':
        return 'es';
      case 'French':
        return 'fr';
      case 'German':
        return 'de';
      case 'Italian':
        return 'it';
      case 'Portuguese':
        return 'pt';
      case 'Russian':
        return 'ru';
      case 'Chinese':
        return 'zh';
      case 'Japanese':
        return 'ja';
      case 'Hindi':
        return 'hi';
      default:
        return 'en';
    }
  }

  /// Update currency
  Future<void> updateCurrency(String currency, String symbol) async {
    final currentState = await future;
    if (currentState.currency == currency &&
        currentState.currencySymbol == symbol) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(PreferenceKeys.currency, currency);
      await prefs.setString(PreferenceKeys.currencySymbol, symbol);

      // Update PreferenceService for synchronization
      try {
        final preferenceService = serviceLocator<PreferenceService>();
        await preferenceService.setCurrency(currency, symbol);
      } catch (e) {
        // Handle error silently
      }

      // Update state
      state = AsyncValue.data(currentState.copyWith(
        currency: currency,
        currencySymbol: symbol,
      ));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update alert threshold
  Future<void> updateAlertThreshold(double threshold) async {
    final currentState = await future;
    if (currentState.alertThreshold == threshold) return;

    // Validate range
    if (threshold < 1.00 || threshold > 999.99) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(PreferenceKeys.alertThreshold, threshold);

      // Update PreferenceService for synchronization
      try {
        final preferenceService = serviceLocator<PreferenceService>();
        await preferenceService.setAlertThreshold(threshold);
      } catch (e) {
        // Handle error silently
      }

      // Update state
      state = AsyncValue.data(currentState.copyWith(alertThreshold: threshold));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update days in advance
  Future<void> updateDaysInAdvance(int days) async {
    final currentState = await future;
    if (currentState.daysInAdvance == days) return;

    // Validate range
    if (days < 1 || days > 99) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(PreferenceKeys.daysInAdvance, days);

      // Update PreferenceService for synchronization
      try {
        final preferenceService = serviceLocator<PreferenceService>();
        await preferenceService.setDaysInAdvance(days);
      } catch (e) {
        // Handle error silently
      }

      // Update state
      state = AsyncValue.data(currentState.copyWith(daysInAdvance: days));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update date format
  Future<void> updateDateFormat(String format) async {
    final currentState = await future;
    if (currentState.dateFormat == format) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(PreferenceKeys.dateFormat, format);

      // Update PreferenceService for synchronization
      try {
        final preferenceService = serviceLocator<PreferenceService>();
        await preferenceService.setDateFormat(DateFormat.fromString(format));
      } catch (e) {
        // Handle error silently
      }

      // Update state
      state = AsyncValue.data(currentState.copyWith(dateFormat: format));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update show time with date
  Future<void> updateShowTimeWithDate(bool show) async {
    final currentState = await future;
    if (currentState.showTimeWithDate == show) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(PreferenceKeys.showTimeWithDate, show);

      // Update PreferenceService for synchronization
      try {
        final preferenceService = serviceLocator<PreferenceService>();
        await preferenceService.setShowTimeWithDate(show);
      } catch (e) {
        // Handle error silently
      }

      // Update state
      state = AsyncValue.data(currentState.copyWith(showTimeWithDate: show));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update notifications enabled
  Future<void> updateNotificationsEnabled(bool enabled) async {
    final currentState = await future;
    if (currentState.notificationsEnabled == enabled) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(PreferenceKeys.notificationsEnabled, enabled);

      // Update state
      state =
          AsyncValue.data(currentState.copyWith(notificationsEnabled: enabled));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update reminders enabled
  Future<void> updateRemindersEnabled(bool enabled) async {
    final currentState = await future;
    if (currentState.remindersEnabled == enabled) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(PreferenceKeys.remindersEnabled, enabled);

      // Clear start date/time when reminders disabled
      if (!enabled) {
        await updateReminderStartDateTime(null);
      }

      // Update state
      state = AsyncValue.data(currentState.copyWith(remindersEnabled: enabled));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update low balance alerts enabled
  Future<void> updateLowBalanceAlertsEnabled(bool enabled) async {
    final currentState = await future;
    if (currentState.lowBalanceAlertsEnabled == enabled) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(PreferenceKeys.lowBalanceAlertsEnabled, enabled);

      // Update state
      state = AsyncValue.data(
          currentState.copyWith(lowBalanceAlertsEnabled: enabled));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update time to top up alerts enabled
  Future<void> updateTimeToTopUpAlertsEnabled(bool enabled) async {
    final currentState = await future;
    if (currentState.timeToTopUpAlertsEnabled == enabled) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(PreferenceKeys.timeToTopUpAlertsEnabled, enabled);

      // Update state
      state = AsyncValue.data(
          currentState.copyWith(timeToTopUpAlertsEnabled: enabled));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update invalid record alerts enabled
  Future<void> updateInvalidRecordAlertsEnabled(bool enabled) async {
    final currentState = await future;
    if (currentState.invalidRecordAlertsEnabled == enabled) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(PreferenceKeys.invalidRecordAlertsEnabled, enabled);

      // Update state
      state = AsyncValue.data(
          currentState.copyWith(invalidRecordAlertsEnabled: enabled));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update reminder start date time
  Future<void> updateReminderStartDateTime(DateTime? dateTime) async {
    final currentState = await future;
    if (currentState.reminderStartDateTime == dateTime) return;

    try {
      final prefs = await SharedPreferences.getInstance();

      if (dateTime != null) {
        await prefs.setString(
            PreferenceKeys.reminderStartDateTime, dateTime.toIso8601String());
      } else {
        await prefs.remove(PreferenceKeys.reminderStartDateTime);
      }

      // Update state
      state = AsyncValue.data(
          currentState.copyWith(reminderStartDateTime: dateTime));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update theme mode
  Future<void> updateThemeMode(AppThemeMode mode) async {
    final currentState = await future;
    if (currentState.themeMode == mode) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(PreferenceKeys.themeMode, mode.toString());

      // Theme changes now handled by Riverpod theme provider

      // Update state
      state = AsyncValue.data(currentState.copyWith(themeMode: mode));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
}
