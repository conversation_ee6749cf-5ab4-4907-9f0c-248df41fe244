// File: lib/features/splash/presentation/screens/splash_screen.dart
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:provider/provider.dart' as provider;
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/providers/localization_provider.dart';
import '../../../../core/providers/preference_provider.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/permission_helper.dart';
import '../../../notifications/presentation/providers/notification_provider.dart';
import '../widgets/splash_animation.dart';

/// The splash screen of the app
class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  // Create logger instance for this screen
  final Logger logger = Logger('SplashScreen');
  // Create an instance of the PermissionHelper
  final PermissionHelper _permissionHelper = PermissionHelper();
  String _statusMessage = 'Initializing...';

  @override
  void initState() {
    super.initState();
    _checkSetupStatus();
  }

  void _updateStatus(String message) {
    if (mounted) {
      setState(() {
        _statusMessage = message;
      });
    }
  }

  Future<void> _checkSetupStatus() async {
    try {
      logger.i('SplashScreen: Checking setup status...');
      // Simulate a delay for the splash screen
      await Future.delayed(const Duration(seconds: 2));

      if (!mounted) {
        logger.i('SplashScreen: Widget not mounted, returning');
        return;
      }

      // Request storage permissions before proceeding
      _updateStatus('Checking permissions...');
      final hasPermission =
          await _permissionHelper.checkAndRequestStoragePermission();

      if (!mounted) return;

      if (!hasPermission) {
        logger.w('SplashScreen: Storage permission denied');
        _updateStatus('Storage permission required');
        // Show permission explanation dialog
        await _showPermissionExplanationDialog();
        if (!mounted) return;
      }

      // Initialize localization provider
      _updateStatus('Initializing language settings...');
      if (mounted) {
        try {
          final localizationProvider =
              provider.Provider.of<LocalizationProvider>(context,
                  listen: false);
          await localizationProvider.detectAndSetDeviceLocale();
        } catch (e) {
          logger.w('SplashScreen: Failed to initialize localization: $e');
          // Continue without localization if it fails
        }
      }

      // Check setup status using Riverpod preferences provider
      _updateStatus('Checking setup status...');
      bool isSetupCompleted = false;
      try {
        logger
            .i('SplashScreen: Checking setup status via preferences provider');
        final preferencesAsync = ref.read(preferencesProvider);
        preferencesAsync.when(
          data: (preferences) {
            isSetupCompleted = preferences.isSetupComplete;
            logger.i('SplashScreen: Setup completed = $isSetupCompleted');
          },
          loading: () {
            logger.i('SplashScreen: Preferences still loading, using fallback');
          },
          error: (error, stackTrace) {
            logger.e('SplashScreen: Error loading preferences: $error');
          },
        );
      } catch (e) {
        logger.e('SplashScreen: Error checking setup status: $e');
        // Fallback to direct SharedPreferences check
        try {
          final prefs = await SharedPreferences.getInstance();
          isSetupCompleted =
              prefs.getBool(AppConstants.keySetupCompleted) ?? false;
          logger.i(
              'SplashScreen: Fallback check: isSetupCompleted = $isSetupCompleted');
        } catch (fallbackError) {
          logger.e('SplashScreen: Fallback check failed: $fallbackError');
        }
      }

      if (!mounted) {
        logger.i('SplashScreen: Widget not mounted after check, returning');
        return;
      }

      // Navigate to the appropriate screen
      if (isSetupCompleted) {
        logger.i('SplashScreen: Setup is completed, navigating to Home');

        // Show welcome notification if this is the first time after setup
        try {
          await ref
              .read(notificationProvider.notifier)
              .createWelcomeNotification();
        } catch (e) {
          logger.w('SplashScreen: Failed to create welcome notification: $e');
          // Continue navigation even if notification fails
        }

        // Check mounted after async operation before using context
        if (!mounted) return;

        // Navigate to home screen
        Navigator.of(context).pushReplacementNamed(AppConstants.routeHome);
      } else {
        logger.i('SplashScreen: Setup not completed, navigating to Welcome');
        if (mounted) {
          Navigator.of(context).pushReplacementNamed(AppConstants.routeWelcome);
        }
      }
    } catch (e) {
      logger.e('SplashScreen: Error checking setup status: $e');
      // Fallback to Welcome screen in case of error
      if (mounted) {
        logger.i('SplashScreen: Navigating to Welcome due to error');
        Navigator.of(context).pushReplacementNamed(AppConstants.routeWelcome);
      }
    }
  }

  /// Show a dialog explaining why storage permission is needed
  Future<void> _showPermissionExplanationDialog() async {
    if (!mounted) return;

    bool shouldTryAgain = false;

    // Show the initial dialog
    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Storage Permission Required'),
          content: const SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text(
                  'Lekky needs access to your device storage to find and restore backup files.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(height: 12),
                Text(
                  'Without this permission, you won\'t be able to restore your previous data.',
                  style: TextStyle(fontSize: 16),
                ),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Continue Anyway'),
              onPressed: () {
                Navigator.of(dialogContext).pop();
                shouldTryAgain = false;
              },
            ),
            TextButton(
              child: const Text('Try Again'),
              onPressed: () {
                Navigator.of(dialogContext).pop();
                shouldTryAgain = true;
              },
            ),
          ],
        );
      },
    );

    // If user wants to try again and the widget is still mounted
    if (shouldTryAgain && mounted) {
      final hasPermission =
          await _permissionHelper.checkAndRequestStoragePermission();

      // Update status based on permission result
      if (!mounted) return;

      if (!hasPermission) {
        _updateStatus('Permission denied. Continuing anyway...');
      } else {
        _updateStatus('Permission granted. Checking for backup files...');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('SplashScreen: Building UI');
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          color: Color(0xFF003087), // Deep blue to match welcome screen
        ),
        child: Stack(
          children: [
            const Center(
              child: SplashAnimation(),
            ),
            // Status message
            Positioned(
              bottom: 60,
              left: 0,
              right: 0,
              child: Center(
                child: Text(
                  _statusMessage,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            // Debug overlay in debug mode only
            if (kDebugMode)
              Positioned(
                bottom: 10,
                left: 10,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    'Debug Build',
                    style: TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
