// File: test/features/calculations/average_calculator_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/utils/average_calculator.dart';
import 'package:lekky/core/models/meter_entry.dart';
import '../../helpers/test_data_helper.dart';

void main() {
  group('AverageCalculator', () {
    group('calculateTotalAverage', () {
      test('should calculate total average correctly with sample data', () {
        // Arrange
        final entries = TestDataHelper.createSampleMeterReadings();

        // Act
        final result = AverageCalculator.calculateTotalAverage(entries);

        // Assert
        expect(result, isA<double>());
        expect(result, greaterThanOrEqualTo(0.0));
      });

      test('should handle empty list', () {
        // Arrange
        final entries = <MeterEntry>[];

        // Act
        final result = AverageCalculator.calculateTotalAverage(entries);

        // Assert
        expect(result, equals(0.0));
      });

      test('should handle single entry', () {
        // Arrange
        final entries = [
          TestDataHelper.createMeterReading(
            id: 1,
            date: DateTime(2023, 1, 1),
            reading: 100.0,
          )
        ];

        // Act
        final result = AverageCalculator.calculateTotalAverage(entries);

        // Assert
        expect(result, equals(0.0)); // Need at least 2 readings
      });

      test('should calculate correct average for two entries', () {
        // Arrange
        final entries = [
          TestDataHelper.createMeterReading(
            id: 1,
            date: DateTime(2023, 1, 1),
            reading: 100.0,
          ),
          TestDataHelper.createMeterReading(
            id: 2,
            date: DateTime(2023, 1, 11), // 10 days later
            reading: 80.0, // Used 20 units
          ),
        ];

        // Act
        final result = AverageCalculator.calculateTotalAverage(entries);

        // Assert
        expect(result, equals(2.0)); // 20 units / 10 days = 2.0 per day
      });
    });

    group('calculateShortAverage', () {
      test('should calculate short average correctly', () {
        // Arrange
        final entries = TestDataHelper.createSampleMeterReadings();

        // Act - Test with index 1 (second entry)
        final result = AverageCalculator.calculateShortAverage(entries, 1);

        // Assert
        expect(result, isA<double>());
        expect(result, greaterThanOrEqualTo(0.0));
      });

      test('should handle invalid index', () {
        // Arrange
        final entries = TestDataHelper.createSampleMeterReadings();

        // Act
        final result = AverageCalculator.calculateShortAverage(entries, -1);

        // Assert
        expect(result, equals(0.0));
      });

      test('should handle entries with top-ups', () {
        // Arrange
        final entries = TestDataHelper.createSampleEntriesWithTopUps();

        // Act
        final result = AverageCalculator.calculateShortAverage(entries, 2);

        // Assert
        expect(result, isA<double>());
        expect(result, greaterThanOrEqualTo(0.0));
      });
    });

    group('calculateProjectedBalance', () {
      test('should calculate projected balance correctly', () {
        // Arrange
        const lastMeterReading = 100.0;
        const topUpsSinceLastReading = 20.0;
        final lastReadingDate =
            DateTime.now().subtract(const Duration(days: 5));
        const averageUsage = 2.0;

        // Act
        final result = AverageCalculator.calculateProjectedBalance(
          lastMeterReading: lastMeterReading,
          topUpsSinceLastReading: topUpsSinceLastReading,
          lastReadingDate: lastReadingDate,
          averageUsage: averageUsage,
        );

        // Assert
        expect(result, isA<double>());
        expect(result, greaterThanOrEqualTo(0.0));
        // Should be approximately: 100 + 20 - (5 * 2) = 110
        expect(
            result, closeTo(110.0, 5.0)); // Allow some tolerance for precision
      });

      test('should return last reading plus top-ups when usage is zero', () {
        // Arrange
        const lastMeterReading = 100.0;
        const topUpsSinceLastReading = 20.0;
        final lastReadingDate =
            DateTime.now().subtract(const Duration(days: 5));
        const averageUsage = 0.0;

        // Act
        final result = AverageCalculator.calculateProjectedBalance(
          lastMeterReading: lastMeterReading,
          topUpsSinceLastReading: topUpsSinceLastReading,
          lastReadingDate: lastReadingDate,
          averageUsage: averageUsage,
        );

        // Assert
        expect(result, equals(120.0)); // 100 + 20
      });

      test('should return last reading plus top-ups for recent readings', () {
        // Arrange
        const lastMeterReading = 100.0;
        const topUpsSinceLastReading = 20.0;
        final lastReadingDate =
            DateTime.now().subtract(const Duration(minutes: 30));
        const averageUsage = 2.0;

        // Act
        final result = AverageCalculator.calculateProjectedBalance(
          lastMeterReading: lastMeterReading,
          topUpsSinceLastReading: topUpsSinceLastReading,
          lastReadingDate: lastReadingDate,
          averageUsage: averageUsage,
        );

        // Assert
        expect(result, equals(120.0)); // 100 + 20, no usage deducted
      });

      test('should return zero when projected balance is negative', () {
        // Arrange
        const lastMeterReading = 10.0;
        const topUpsSinceLastReading = 5.0;
        final lastReadingDate =
            DateTime.now().subtract(const Duration(days: 10));
        const averageUsage = 5.0;

        // Act
        final result = AverageCalculator.calculateProjectedBalance(
          lastMeterReading: lastMeterReading,
          topUpsSinceLastReading: topUpsSinceLastReading,
          lastReadingDate: lastReadingDate,
          averageUsage: averageUsage,
        );

        // Assert
        expect(result, equals(0.0)); // Would be 15 - 50 = -35, clamped to 0
      });

      test('should handle zero average usage', () {
        // Arrange
        const lastMeterReading = 100.0;
        const topUpsSinceLastReading = 20.0;
        final lastReadingDate =
            DateTime.now().subtract(const Duration(days: 5));
        const averageUsage = 0.0;

        // Act
        final result = AverageCalculator.calculateProjectedBalance(
          lastMeterReading: lastMeterReading,
          topUpsSinceLastReading: topUpsSinceLastReading,
          lastReadingDate: lastReadingDate,
          averageUsage: averageUsage,
        );

        // Assert
        expect(result, equals(120.0)); // 100 + 20 when no usage
      });
    });

    group('calculateDaysRemaining', () {
      test('should calculate days remaining correctly', () {
        // Arrange
        const lastMeterReading = 100.0;
        const topUpsSinceLastReading = 20.0;
        final lastReadingDate =
            DateTime.now().subtract(const Duration(days: 5));
        const averageUsage = 2.0;

        // Act
        final result = AverageCalculator.calculateDaysRemaining(
          lastMeterReading: lastMeterReading,
          topUpsSinceLastReading: topUpsSinceLastReading,
          lastReadingDate: lastReadingDate,
          averageUsage: averageUsage,
        );

        // Assert
        expect(result, isA<double>());
        expect(result, greaterThan(0.0));
        // Projected balance: 100 + 20 - (5 * 2) = 110
        // Days remaining: 110 / 2 = 55
        expect(result, closeTo(55.0, 5.0)); // Allow tolerance for precision
      });

      test('should return null when average usage is zero', () {
        // Arrange
        const lastMeterReading = 100.0;
        const topUpsSinceLastReading = 20.0;
        final lastReadingDate =
            DateTime.now().subtract(const Duration(days: 5));
        const averageUsage = 0.0;

        // Act
        final result = AverageCalculator.calculateDaysRemaining(
          lastMeterReading: lastMeterReading,
          topUpsSinceLastReading: topUpsSinceLastReading,
          lastReadingDate: lastReadingDate,
          averageUsage: averageUsage,
        );

        // Assert
        expect(result, isNull);
      });

      test('should return null when average usage is negative', () {
        // Arrange
        const lastMeterReading = 100.0;
        const topUpsSinceLastReading = 20.0;
        final lastReadingDate =
            DateTime.now().subtract(const Duration(days: 5));
        const averageUsage = -1.0;

        // Act
        final result = AverageCalculator.calculateDaysRemaining(
          lastMeterReading: lastMeterReading,
          topUpsSinceLastReading: topUpsSinceLastReading,
          lastReadingDate: lastReadingDate,
          averageUsage: averageUsage,
        );

        // Assert
        expect(result, isNull);
      });

      test('should handle recent readings correctly', () {
        // Arrange
        const lastMeterReading = 100.0;
        const topUpsSinceLastReading = 20.0;
        final lastReadingDate =
            DateTime.now().subtract(const Duration(minutes: 30));
        const averageUsage = 2.0;

        // Act
        final result = AverageCalculator.calculateDaysRemaining(
          lastMeterReading: lastMeterReading,
          topUpsSinceLastReading: topUpsSinceLastReading,
          lastReadingDate: lastReadingDate,
          averageUsage: averageUsage,
        );

        // Assert
        expect(result, isA<double>());
        // For recent readings, projected balance = 100 + 20 = 120
        // Days remaining: 120 / 2 = 60
        expect(result, equals(60.0));
      });

      test('should handle zero projected balance', () {
        // Arrange
        const lastMeterReading = 10.0;
        const topUpsSinceLastReading = 5.0;
        final lastReadingDate =
            DateTime.now().subtract(const Duration(days: 10));
        const averageUsage = 5.0;

        // Act
        final result = AverageCalculator.calculateDaysRemaining(
          lastMeterReading: lastMeterReading,
          topUpsSinceLastReading: topUpsSinceLastReading,
          lastReadingDate: lastReadingDate,
          averageUsage: averageUsage,
        );

        // Assert
        expect(result, isA<double>());
        // Projected balance would be negative, clamped to 0
        // Days remaining: 0 / 5 = 0
        expect(result, equals(0.0));
      });

      test('should handle edge case with very small usage', () {
        // Arrange
        const lastMeterReading = 100.0;
        const topUpsSinceLastReading = 0.0;
        final lastReadingDate =
            DateTime.now().subtract(const Duration(days: 1));
        const averageUsage = 0.01; // Very small usage

        // Act
        final result = AverageCalculator.calculateDaysRemaining(
          lastMeterReading: lastMeterReading,
          topUpsSinceLastReading: topUpsSinceLastReading,
          lastReadingDate: lastReadingDate,
          averageUsage: averageUsage,
        );

        // Assert
        expect(result, isA<double>());
        expect(result, greaterThan(0.0));
        // Should be a large number of days
        expect(result, greaterThan(1000.0));
      });
    });

    group('performance tests', () {
      test('should handle large dataset efficiently', () {
        // Arrange
        final entries = TestDataHelper.createLargeDataset();
        final stopwatch = Stopwatch()..start();

        // Act
        final totalAverage = AverageCalculator.calculateTotalAverage(entries);
        final shortAverage =
            AverageCalculator.calculateShortAverage(entries, 100);
        stopwatch.stop();

        // Assert
        expect(totalAverage, isA<double>());
        expect(shortAverage, isA<double>());
        expect(stopwatch.elapsedMilliseconds, lessThan(100)); // Should be fast
      });
    });
  });
}
