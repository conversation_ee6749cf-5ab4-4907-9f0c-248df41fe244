import 'notification.dart';

/// Immutable state for the Notification feature
class NotificationState {
  /// List of all notifications
  final List<AppNotification> notifications;

  /// Count of unread notifications
  final int unreadCount;

  /// Loading state
  final bool isLoading;

  /// Error message
  final String? errorMessage;

  /// Whether notifications are enabled globally
  final bool notificationsEnabled;

  /// Whether low balance alerts are enabled
  final bool lowBalanceAlertsEnabled;

  /// Whether time to top up alerts are enabled
  final bool timeToTopUpAlertsEnabled;

  /// Whether invalid record alerts are enabled
  final bool invalidRecordAlertsEnabled;

  /// Constructor
  const NotificationState({
    this.notifications = const [],
    this.unreadCount = 0,
    this.isLoading = false,
    this.errorMessage,
    this.notificationsEnabled = true,
    this.lowBalanceAlertsEnabled = true,
    this.timeToTopUpAlertsEnabled = true,
    this.invalidRecordAlertsEnabled = true,
  });

  /// Initial notification state
  factory NotificationState.initial() => const NotificationState();

  /// Create a copy with some fields changed
  NotificationState copyWith({
    List<AppNotification>? notifications,
    int? unreadCount,
    bool? isLoading,
    String? errorMessage,
    bool? notificationsEnabled,
    bool? lowBalanceAlertsEnabled,
    bool? timeToTopUpAlertsEnabled,
    bool? invalidRecordAlertsEnabled,
  }) {
    return NotificationState(
      notifications: notifications ?? this.notifications,
      unreadCount: unreadCount ?? this.unreadCount,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      lowBalanceAlertsEnabled:
          lowBalanceAlertsEnabled ?? this.lowBalanceAlertsEnabled,
      timeToTopUpAlertsEnabled:
          timeToTopUpAlertsEnabled ?? this.timeToTopUpAlertsEnabled,
      invalidRecordAlertsEnabled:
          invalidRecordAlertsEnabled ?? this.invalidRecordAlertsEnabled,
    );
  }
}

/// Extension methods for NotificationState
extension NotificationStateX on NotificationState {
  /// Check if there are notifications to display
  bool get hasNotifications => notifications.isNotEmpty;

  /// Check if there's an error
  bool get hasError => errorMessage != null;

  /// Get unread notifications
  List<AppNotification> get unreadNotifications =>
      notifications.where((notification) => !notification.isRead).toList();

  /// Get read notifications
  List<AppNotification> get readNotifications =>
      notifications.where((notification) => notification.isRead).toList();

  /// Check if there are unread notifications
  bool get hasUnreadNotifications => unreadCount > 0;

  /// Get notifications by type
  List<AppNotification> getNotificationsByType(NotificationType type) =>
      notifications.where((notification) => notification.type == type).toList();

  /// Get notifications sorted by priority and timestamp
  List<AppNotification> get sortedNotifications {
    final sorted = List<AppNotification>.from(notifications);
    sorted.sort((a, b) {
      // First sort by read status (unread first)
      if (a.isRead != b.isRead) {
        return a.isRead ? 1 : -1;
      }
      // Then by priority (higher priority first)
      final priorityComparison = b.priorityLevel.compareTo(a.priorityLevel);
      if (priorityComparison != 0) {
        return priorityComparison;
      }
      // Finally by timestamp (newest first)
      return b.timestamp.compareTo(a.timestamp);
    });
    return sorted;
  }

  /// Check if a specific notification type is enabled
  bool isNotificationTypeEnabled(NotificationType type) {
    if (!notificationsEnabled) return false;

    switch (type) {
      case NotificationType.lowBalance:
        return lowBalanceAlertsEnabled;
      case NotificationType.timeToTopUp:
        return timeToTopUpAlertsEnabled;
      case NotificationType.invalidRecord:
        return invalidRecordAlertsEnabled;
      case NotificationType.readingReminder:
      case NotificationType.welcome:
        return true; // These are always enabled if notifications are enabled
    }
  }
}
